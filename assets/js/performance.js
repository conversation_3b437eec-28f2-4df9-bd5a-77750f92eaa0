/**
 * 性能优化脚本
 * 实现资源懒加载、预加载和性能监控
 */

(function() {
    'use strict';

    // 性能监控
    const PerformanceMonitor = {
        // 记录页面加载时间
        recordPageLoad: function() {
            window.addEventListener('load', function() {
                // 使用更可靠的性能API
                if (window.performance) {
                    let loadTime = 0;
                    let domReady = 0;

                    // 优先使用Navigation Timing API Level 2
                    if (window.performance.getEntriesByType) {
                        const navEntries = window.performance.getEntriesByType('navigation');
                        if (navEntries.length > 0) {
                            const nav = navEntries[0];
                            loadTime = Math.round(nav.loadEventEnd - nav.fetchStart);
                            domReady = Math.round(nav.domContentLoadedEventEnd - nav.fetchStart);
                        }
                    }

                    // 降级到Navigation Timing API Level 1
                    if (loadTime === 0 && window.performance.timing) {
                        const timing = window.performance.timing;
                        if (timing.loadEventEnd > 0 && timing.navigationStart > 0) {
                            loadTime = timing.loadEventEnd - timing.navigationStart;
                            domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
                        }
                    }

                    if (loadTime > 0) {
                        console.log('页面加载时间:', loadTime + 'ms');
                        console.log('DOM就绪时间:', domReady + 'ms');

                        // 可以发送到分析服务
                        // this.sendAnalytics('page_load', { loadTime, domReady });
                    }
                }
            });
        },

        // 监控资源加载
        monitorResources: function() {
            if (window.performance && window.performance.getEntriesByType) {
                window.addEventListener('load', function() {
                    const resources = window.performance.getEntriesByType('resource');
                    const slowResources = resources.filter(resource => resource.duration > 1000);
                    
                    if (slowResources.length > 0) {
                        console.warn('慢速资源:', slowResources);
                    }
                });
            }
        },

        // 发送分析数据（示例）
        sendAnalytics: function(event, data) {
            // 这里可以集成Google Analytics或其他分析工具
            // gtag('event', event, data);
        }
    };

    // 图片懒加载
    const LazyLoader = {
        init: function() {
            this.setupIntersectionObserver();
            this.loadCriticalImages();
        },

        setupIntersectionObserver: function() {
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            this.loadImage(img);
                            observer.unobserve(img);
                        }
                    });
                });

                // 观察所有懒加载图片
                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            } else {
                // 降级方案：直接加载所有图片
                this.loadAllImages();
            }
        },

        loadImage: function(img) {
            if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                img.classList.add('loaded');
            }
        },

        loadAllImages: function() {
            document.querySelectorAll('img[data-src]').forEach(img => {
                this.loadImage(img);
            });
        },

        loadCriticalImages: function() {
            // 立即加载首屏关键图片
            document.querySelectorAll('img[data-critical]').forEach(img => {
                this.loadImage(img);
            });
        }
    };

    // 资源预加载器
    const ResourcePreloader = {
        preloadedResources: new Set(),

        preloadResource: function(url, type = 'script') {
            if (this.preloadedResources.has(url)) {
                return Promise.resolve();
            }

            return new Promise((resolve, reject) => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = url;
                link.as = type;
                
                link.onload = () => {
                    this.preloadedResources.add(url);
                    resolve();
                };
                link.onerror = reject;
                
                document.head.appendChild(link);
            });
        },

        loadScript: function(url) {
            if (document.querySelector(`script[src="${url}"]`)) {
                return Promise.resolve();
            }

            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = url;
                script.async = true;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        },

        loadStylesheet: function(url) {
            if (document.querySelector(`link[href="${url}"]`)) {
                return Promise.resolve();
            }

            return new Promise((resolve, reject) => {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = url;
                link.onload = resolve;
                link.onerror = reject;
                document.head.appendChild(link);
            });
        }
    };

    // 连接优化
    const ConnectionOptimizer = {
        init: function() {
            this.addDNSPrefetch();
            this.addPreconnect();
        },

        addDNSPrefetch: function() {
            const domains = [
                'fonts.googleapis.com',
                'fonts.gstatic.com',
                'cdnjs.cloudflare.com',
                'cdn.jsdelivr.net'
            ];

            domains.forEach(domain => {
                if (!document.querySelector(`link[href="//${domain}"]`)) {
                    const link = document.createElement('link');
                    link.rel = 'dns-prefetch';
                    link.href = `//${domain}`;
                    document.head.appendChild(link);
                }
            });
        },

        addPreconnect: function() {
            const domains = [
                'https://fonts.googleapis.com',
                'https://fonts.gstatic.com',
                'https://cdnjs.cloudflare.com'
            ];

            domains.forEach(domain => {
                if (!document.querySelector(`link[href="${domain}"]`)) {
                    const link = document.createElement('link');
                    link.rel = 'preconnect';
                    link.href = domain;
                    link.crossOrigin = 'anonymous';
                    document.head.appendChild(link);
                }
            });
        }
    };

    // 缓存管理
    const CacheManager = {
        // 检查浏览器支持
        isSupported: function() {
            return 'caches' in window;
        },

        // 缓存关键资源
        cacheResources: function() {
            if (!this.isSupported()) return;

            const resourcesToCache = [
                '/assets/css/style.css',
                '/assets/css/responsive.css',
                '/assets/js/config.js',
                '/assets/js/auth.js',
                '/assets/js/main.js'
            ];

            caches.open('idatas-v1').then(cache => {
                cache.addAll(resourcesToCache);
            });
        }
    };

    // 初始化所有优化
    function initPerformanceOptimizations() {
        // 性能监控
        PerformanceMonitor.recordPageLoad();
        PerformanceMonitor.monitorResources();

        // 连接优化
        ConnectionOptimizer.init();

        // DOM加载完成后初始化懒加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                LazyLoader.init();
            });
        } else {
            LazyLoader.init();
        }

        // 页面加载完成后缓存资源
        window.addEventListener('load', function() {
            CacheManager.cacheResources();
        });
    }

    // 立即执行初始化
    initPerformanceOptimizations();

    // 暴露API供其他脚本使用
    window.PerformanceOptimizer = {
        preloadResource: ResourcePreloader.preloadResource.bind(ResourcePreloader),
        loadScript: ResourcePreloader.loadScript.bind(ResourcePreloader),
        loadStylesheet: ResourcePreloader.loadStylesheet.bind(ResourcePreloader),
        loadImage: LazyLoader.loadImage.bind(LazyLoader)
    };

})();
