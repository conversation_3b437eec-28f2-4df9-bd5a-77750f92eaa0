/* 本地图标样式 - 替代FontAwesome的关键图标 */

/* 基础图标样式 */
.icon {
    display: inline-block;
    width: 1em;
    height: 1em;
    vertical-align: -0.125em;
    fill: currentColor;
}

/* SVG图标基础样式 */
.svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.125em;
    fill: currentColor;
}

/* 常用图标的CSS实现 */
.icon-search::before {
    content: "🔍";
    font-style: normal;
}

.icon-user::before {
    content: "👤";
    font-style: normal;
}

.icon-crown::before {
    content: "👑";
    font-style: normal;
}

.icon-play::before {
    content: "▶️";
    font-style: normal;
}

.icon-close::before {
    content: "✕";
    font-style: normal;
    font-weight: bold;
}

.icon-menu::before {
    content: "☰";
    font-style: normal;
}

.icon-home::before {
    content: "🏠";
    font-style: normal;
}

.icon-logout::before {
    content: "🚪";
    font-style: normal;
}

.icon-check::before {
    content: "✓";
    font-style: normal;
    color: #34C759;
}

.icon-times::before {
    content: "✕";
    font-style: normal;
    color: #FF3B30;
}

.icon-chevron-up::before {
    content: "▲";
    font-style: normal;
}

.icon-chevron-down::before {
    content: "▼";
    font-style: normal;
}

.icon-chevron-right::before {
    content: "▶";
    font-style: normal;
}

.icon-bullhorn::before {
    content: "📢";
    font-style: normal;
}

.icon-gem::before {
    content: "💎";
    font-style: normal;
}

.icon-code::before {
    content: "💻";
    font-style: normal;
}

.icon-qq::before {
    content: "Q";
    font-style: normal;
    background: #12B7F5;
    color: white;
    border-radius: 50%;
    width: 1.2em;
    height: 1.2em;
    text-align: center;
    line-height: 1.2em;
    font-weight: bold;
}

/* 加载状态图标 */
.icon-spinner {
    display: inline-block;
    width: 1em;
    height: 1em;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007AFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式图标大小 */
.icon-sm {
    font-size: 0.875rem;
}

.icon-lg {
    font-size: 1.25rem;
}

.icon-xl {
    font-size: 1.5rem;
}

.icon-2x {
    font-size: 2rem;
}

/* 图标动画效果 */
.icon-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.icon-bounce {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translateY(0);
    }
    40%, 43% {
        transform: translateY(-10px);
    }
    70% {
        transform: translateY(-5px);
    }
    90% {
        transform: translateY(-2px);
    }
}

/* 图标颜色变体 */
.icon-primary {
    color: #007AFF;
}

.icon-success {
    color: #34C759;
}

.icon-warning {
    color: #FF9500;
}

.icon-danger {
    color: #FF3B30;
}

.icon-secondary {
    color: #8E8E93;
}

/* 图标按钮样式 */
.icon-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.icon-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.icon-btn:active {
    transform: scale(0.95);
}

/* 图标组合 */
.icon-with-text {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

/* 图标徽章 */
.icon-badge {
    position: relative;
}

.icon-badge::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #FF3B30;
    border-radius: 50%;
    border: 2px solid white;
}

/* 图标加载状态 */
.icon-loading {
    opacity: 0.6;
    pointer-events: none;
}

.icon-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007AFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* QRCode 加载状态样式 */
.qr-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    color: #6c757d;
    font-size: 14px;
}

.qr-loading::before {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid #dee2e6;
    border-top: 2px solid #007AFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

.qr-error {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: #f8d7da;
    border: 2px solid #f5c6cb;
    border-radius: 8px;
    color: #721c24;
    font-size: 14px;
}

.qr-error::before {
    content: '⚠️';
    margin-right: 8px;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .icon-btn:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .icon-badge::after {
        border-color: #1c1c1e;
    }

    .qr-loading {
        background: #2c2c2e;
        border-color: #48484a;
        color: #8e8e93;
    }

    .qr-error {
        background: #3a2a2b;
        border-color: #5a3a3b;
        color: #ff6b6b;
    }
}

/* 视频懒加载样式 */
.video-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.video-placeholder:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    border-color: #007AFF;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 122, 255, 0.15);
}

.placeholder-content {
    text-align: center;
    padding: 40px 20px;
}

.play-button {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
}

.play-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4);
}

.play-button svg {
    margin-left: 4px; /* 视觉居中调整 */
}

.video-placeholder h3 {
    color: #495057;
    margin-bottom: 8px;
    font-size: 1.5rem;
    font-weight: 600;
}

.video-placeholder p {
    color: #6c757d;
    margin-bottom: 16px;
    font-size: 1rem;
}

.video-info {
    display: flex;
    justify-content: center;
    gap: 20px;
    font-size: 0.9rem;
    color: #6c757d;
}

/* 视频加载状态 */
.video-loading {
    text-align: center;
    padding: 40px 20px;
}

.video-loading h3 {
    color: #495057;
    margin: 20px 0 8px;
    font-size: 1.3rem;
}

.video-loading p {
    color: #6c757d;
    margin-bottom: 20px;
}

.loading-progress {
    width: 200px;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    margin: 0 auto;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007AFF, #0056CC);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 2px;
}

/* 视频错误状态 */
.video-error {
    text-align: center;
    padding: 40px 20px;
}

.error-icon {
    font-size: 3rem;
    margin-bottom: 16px;
}

.video-error h3 {
    color: #dc3545;
    margin-bottom: 8px;
    font-size: 1.3rem;
}

.video-error p {
    color: #6c757d;
    margin-bottom: 20px;
}

.retry-btn {
    background: #007AFF;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s ease;
}

.retry-btn:hover {
    background: #0056CC;
}

/* 教程提示样式 */
.tutorial-tips {
    background: #f8f9fa;
    border-left: 4px solid #007AFF;
    padding: 12px 16px;
    margin-top: 16px;
    border-radius: 0 6px 6px 0;
}

.tutorial-tips p {
    margin: 0;
    font-size: 0.9rem;
    color: #495057;
}

/* 响应式适配 */
@media (max-width: 768px) {
    .video-placeholder {
        min-height: 300px;
    }

    .placeholder-content {
        padding: 30px 15px;
    }

    .play-button {
        width: 60px;
        height: 60px;
    }

    .play-button svg {
        width: 40px;
        height: 40px;
    }

    .video-placeholder h3 {
        font-size: 1.3rem;
    }

    .video-info {
        flex-direction: column;
        gap: 8px;
    }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .video-placeholder {
        background: linear-gradient(135deg, #2c2c2e 0%, #1c1c1e 100%);
        border-color: #48484a;
    }

    .video-placeholder:hover {
        background: linear-gradient(135deg, #1c1c1e 0%, #000000 100%);
    }

    .video-placeholder h3 {
        color: #ffffff;
    }

    .video-placeholder p,
    .video-info {
        color: #8e8e93;
    }

    .video-loading h3 {
        color: #ffffff;
    }

    .video-loading p {
        color: #8e8e93;
    }

    .loading-progress {
        background: #48484a;
    }

    .tutorial-tips {
        background: #2c2c2e;
        border-left-color: #007AFF;
    }

    .tutorial-tips p {
        color: #ffffff;
    }
}
