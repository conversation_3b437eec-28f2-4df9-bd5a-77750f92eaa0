/* 关键CSS - 首屏渲染必需样式 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS变量 - 仅关键颜色 */
:root {
    --primary-color: #007AFF;
    --primary-dark: #0056CC;
    --bg-primary: #FFFFFF;
    --bg-secondary: #F2F2F7;
    --text-primary: #000000;
    --text-secondary: #3C3C43;
    --border-color: #E5E5EA;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --radius-medium: 12px;
    --transition-fast: 0.2s ease;
}

/* 基础字体 */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 16px;
    overflow-x: hidden;
}

/* 导航栏 - 关键样式 */
.navbar {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-light);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 700;
    font-size: 24px;
    color: var(--primary-color);
}

.logo-img {
    width: 32px;
    height: 32px;
}

/* 主要内容区域 */
.main-content {
    min-height: calc(100vh - 70px);
    padding: 20px 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 欢迎区域 */
.welcome-section {
    text-align: center;
    padding: 60px 0;
    background: var(--bg-primary);
    border-radius: var(--radius-medium);
    margin-bottom: 40px;
    box-shadow: var(--shadow-light);
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.welcome-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式基础 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 16px;
        height: 60px;
    }
    
    .container {
        padding: 0 16px;
    }
    
    .welcome-title {
        font-size: 2rem;
    }
    
    .welcome-subtitle {
        font-size: 1.1rem;
    }
    
    .welcome-section {
        padding: 40px 0;
        margin-bottom: 30px;
    }
}

/* 字体加载优化 */
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: local('Inter Regular'), local('Inter-Regular');
}

@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: local('Inter SemiBold'), local('Inter-SemiBold');
}

@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: local('Inter Bold'), local('Inter-Bold');
}

/* 防止布局偏移 */
img {
    max-width: 100%;
    height: auto;
}

/* 隐藏非关键内容直到加载完成 */
.tools-section,
.announcement-banner,
.modal,
.floating-vip-banner,
.mobile-bottom-bar {
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.loaded .tools-section,
.loaded .announcement-banner,
.loaded .modal,
.loaded .floating-vip-banner,
.loaded .mobile-bottom-bar {
    opacity: 1;
}

/* 预加载状态 */
.preload * {
    transition: none !important;
}

/* 关键按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-medium);
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    gap: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* 搜索框基础样式 */
.nav-search {
    flex: 1;
    max-width: 500px;
    margin: 0 40px;
    position: relative;
}

.search-container {
    position: relative;
    width: 100%;
}

.search-input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-medium);
    font-size: 16px;
    background: var(--bg-primary);
    transition: var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    pointer-events: none;
}

/* 移动端导航优化 */
@media (max-width: 768px) {
    .nav-search {
        display: none;
    }
    
    .nav-actions {
        display: none;
    }
    
    .mobile-menu-btn {
        display: block;
    }
}
