/* 响应式设计 - 移动端适配 */

/* 平板设备 */
@media (max-width: 1024px) {
    .container {
        padding: 0 16px;
    }
    
    .nav-container {
        padding: 12px 16px;
    }
    
    .nav-search {
        margin: 0 20px;
        max-width: 300px;
    }
    
    .tools-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .welcome-title {
        font-size: 32px;
    }
    
    .section-title {
        font-size: 24px;
    }
}

/* 移动设备 */
@media (max-width: 768px) {
    .nav-container {
        flex-wrap: wrap;
        gap: 12px;
    }
    
    .nav-search {
        order: 3;
        flex: 1 1 100%;
        margin: 0;
        max-width: none;
    }
    
    .nav-actions {
        gap: 8px;
    }
    
    .nav-actions span {
        display: none;
    }
    
    .tutorial-btn, .profile-btn, .logout-btn {
        padding: 8px;
        min-width: 40px;
        justify-content: center;
    }
    
    .mobile-menu-btn {
        display: none;
    }
    
    .main-content {
        padding: 20px 0;
    }
    
    .welcome-section {
        margin-bottom: 40px;
    }
    
    .welcome-title {
        font-size: 28px;
        line-height: 1.2;
    }
    
    .welcome-subtitle {
        font-size: 16px;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        margin-bottom: 24px;
    }
    
    .section-title {
        font-size: 22px;
    }
    
    .filter-tabs {
        align-self: stretch;
        justify-content: center;
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .tool-card {
        padding: 20px;
    }
    
    .tool-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
    
    .tool-name {
        font-size: 16px;
    }
    
    .tool-description {
        font-size: 13px;
    }
    
    .modal-content {
        width: calc(100% - 40px);
        margin: 20px;
        max-height: calc(100vh - 40px);
        min-width: 280px;
    }
    
    .modal-header {
        padding: 20px 20px 0;
        margin-bottom: 20px;
    }
    
    .modal-header h3 {
        font-size: 18px;
    }
    
    .modal-body {
        padding: 0 20px 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .btn {
        padding: 12px 20px;
        font-size: 15px;
    }
    
    .toast {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }
    
    .toast.show {
        transform: translateY(0);
    }
}

/* 小屏幕移动设备 */
@media (max-width: 480px) {
    .container {
        padding: 0 12px;
    }
    
    .nav-container {
        padding: 10px 12px;
    }
    
    .nav-logo {
        gap: 8px;
    }
    
    .logo-text {
        font-size: 18px;
    }
    
    .search-input {
        padding: 8px 12px 8px 36px;
        font-size: 14px;
    }
    
    .search-icon {
        left: 12px;
    }
    
    .welcome-title {
        font-size: 24px;
    }
    
    .welcome-subtitle {
        font-size: 15px;
    }
    
    .section-title {
        font-size: 20px;
    }
    
    .filter-tab {
        padding: 6px 12px;
        font-size: 13px;
    }
    
    .tool-card {
        padding: 16px;
    }
    
    .tool-icon {
        width: 36px;
        height: 36px;
        font-size: 18px;
        margin-bottom: 12px;
    }
    
    .tool-name {
        font-size: 15px;
        margin-bottom: 6px;
    }
    
    .tool-description {
        font-size: 12px;
    }
    
    .modal-content {
        border-radius: var(--radius-medium);
        margin: 15px;
        max-height: calc(100vh - 30px);
        width: calc(100% - 30px);
    }
    
    .modal-header {
        padding: 20px 20px 0;
        margin-bottom: 16px;
        position: relative;
    }

    /* 确保关闭按钮有足够的点击区域 */
    .modal-header .modal-close {
        position: absolute;
        top: 12px;
        right: 12px;
        z-index: 10;
    }
    
    .modal-header h3 {
        font-size: 16px;
    }
    
    .modal-close {
        width: 44px;
        height: 44px;
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: -8px;
    }
    
    .modal-body {
        padding: 0 16px 16px;
    }
    
    .form-group {
        margin-bottom: 16px;
    }
    
    .form-group input, .form-group textarea, .form-group select {
        padding: 10px 14px;
        font-size: 15px;
    }
    
    .btn {
        padding: 10px 16px;
        font-size: 14px;
        min-height: 40px;
    }
    
    .video-container video {
        height: 200px;
        object-fit: cover;
    }
    
    .profile-card {
        padding: 16px;
    }
    
    .profile-item {
        padding: 6px 0;
    }
    
    .profile-label, .profile-value {
        font-size: 13px;
    }
    
    .result-container {
        padding: 16px;
        margin-top: 20px;
    }
    
    .result-text {
        font-size: 13px;
    }
}

/* 超小屏幕设备 */
@media (max-width: 360px) {
    .nav-logo .logo-text {
        display: none;
    }
    
    .welcome-title {
        font-size: 22px;
    }
    
    .welcome-subtitle {
        font-size: 14px;
    }
    
    .section-title {
        font-size: 18px;
    }
    
    .filter-tabs {
        gap: 4px;
        padding: 2px;
    }
    
    .filter-tab {
        padding: 4px 8px;
        font-size: 12px;
    }
    
    .tool-card {
        padding: 12px;
    }
    
    .tool-icon {
        width: 32px;
        height: 32px;
        font-size: 16px;
        margin-bottom: 10px;
    }
    
    .tool-name {
        font-size: 14px;
    }
    
    .tool-description {
        font-size: 11px;
    }
}

/* 横屏模式优化 */
@media (max-height: 600px) and (orientation: landscape) {
    .welcome-section {
        margin-bottom: 20px;
    }
    
    .welcome-title {
        font-size: 24px;
        margin-bottom: 8px;
    }
    
    .welcome-subtitle {
        font-size: 14px;
    }
    
    .main-content {
        padding: 20px 0;
    }
    
    .modal-content {
        max-height: 90vh;
    }
    
    .video-container video {
        height: 250px;
    }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1440px) {
    .container {
        max-width: 1400px;
    }
    
    .tools-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 28px;
    }
    
    .tool-card {
        padding: 28px;
    }
    
    .tool-icon {
        width: 52px;
        height: 52px;
        font-size: 26px;
    }
    
    .tool-name {
        font-size: 19px;
    }
    
    .tool-description {
        font-size: 15px;
    }
}

/* 移动端底栏样式 */
@media (max-width: 768px) {
    /* 隐藏桌面端的导航动作 */
    .nav-actions {
        display: none;
    }

    /* 确保PC端按钮在移动端隐藏 */
    .tutorial-btn, .recharge-btn, .profile-btn, .logout-btn {
        display: none;
    }

    /* 隐藏移动端菜单按钮 */
    .mobile-menu-btn {
        display: none;
    }

    /* 为底栏留出空间 */
    .main-content {
        padding-bottom: 80px;
    }

    /* 移动端底栏 */
    .mobile-bottom-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: var(--bg-primary);
        border-top: 1px solid var(--border-color);
        padding: 8px 0;
        z-index: 1000;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(20px);
    }

    .bottom-bar-container {
        display: flex;
        justify-content: space-around;
        align-items: center;
        max-width: 100%;
        margin: 0 auto;
        padding: 0 16px;
    }

    .bottom-bar-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        border-radius: var(--radius-small);
        background: none;
        border: none;
        color: var(--text-tertiary);
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition-fast);
        min-width: 60px;
        text-decoration: none;
    }

    .bottom-bar-item:hover,
    .bottom-bar-item.active {
        color: var(--primary-color);
        background: var(--bg-secondary);
        transform: translateY(-1px);
    }

    .bottom-bar-item i {
        font-size: 18px;
        margin-bottom: 4px;
    }

    .bottom-bar-item span {
        font-size: 11px;
        line-height: 1;
    }

    /* 底栏项目间距优化（4个按钮） */
    .bottom-bar-container {
        justify-content: space-around;
    }

    /* 充值按钮特殊样式 */
    .bottom-bar-item.recharge {
        color: #FFD700;
    }

    .bottom-bar-item.recharge:hover {
        background: linear-gradient(135deg, #FFD700, #FFA500);
        color: #333;
        transform: translateY(-2px);
    }

    /* 底栏动画 */
    .mobile-bottom-bar {
        transition: transform var(--transition-medium);
    }

    /* 安全区域适配 */
    @supports (padding: max(0px)) {
        .mobile-bottom-bar {
            padding-bottom: max(8px, env(safe-area-inset-bottom));
        }
    }

    /* 底栏徽章（可用于通知） */
    .bottom-bar-item .badge {
        position: absolute;
        top: 4px;
        right: 8px;
        background: var(--danger-color);
        color: white;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        font-size: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
    }

    /* 底栏项目相对定位 */
    .bottom-bar-item {
        position: relative;
    }
}

/* 触摸优化 */
@media (hover: none) and (pointer: coarse) {
    .tool-card:hover {
        transform: none;
    }

    .tool-card:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    .btn:hover {
        transform: none;
    }

    .btn:active {
        transform: scale(0.98);
    }
}

/* 安全区域适配（iPhone X等） */
@supports (padding: max(0px)) {
    .navbar {
        padding-left: max(20px, env(safe-area-inset-left));
        padding-right: max(20px, env(safe-area-inset-right));
    }

    .container {
        padding-left: max(12px, env(safe-area-inset-left));
        padding-right: max(12px, env(safe-area-inset-right));
    }

    .modal-content {
        margin-left: max(10px, env(safe-area-inset-left));
        margin-right: max(10px, env(safe-area-inset-right));
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1C1C1E;
        --bg-secondary: #2C2C2E;
        --bg-tertiary: #3A3A3C;
        --bg-card: #2C2C2E;

        --text-primary: #FFFFFF;
        --text-secondary: #EBEBF5;
        --text-tertiary: #EBEBF599;
        --text-quaternary: #EBEBF54D;

        --border-color: #38383A;
        --separator-color: #54545899;
    }

    .tool-card.vip {
        background: linear-gradient(135deg, #2C2C2E, #3A3A3C);
    }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .tool-card:hover {
        transform: none;
    }

    .modal-content {
        transform: none !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-tertiary: #000000;
    }

    .tool-card {
        border-width: 2px;
    }

    .btn {
        border-width: 2px;
    }
}

/* 打印样式 */
@media print {
    .navbar, .modal, .toast, .loading-overlay {
        display: none !important;
    }

    .main-content {
        padding: 0;
    }

    .tool-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .tool-card:hover {
        transform: none;
    }
}
