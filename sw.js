/**
 * Service Worker for iDatas
 * 实现缓存策略和离线支持
 */

const CACHE_NAME = 'idatas-v1.0.0';
const STATIC_CACHE = 'idatas-static-v1';
const DYNAMIC_CACHE = 'idatas-dynamic-v1';

// 需要缓存的静态资源
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/assets/css/style.css',
    '/assets/css/responsive.css',
    '/assets/css/critical.css',
    '/assets/js/config.js',
    '/assets/js/auth.js',
    '/assets/js/main.js',
    '/assets/js/performance.js',
    '/manifest.json'
];

// 需要网络优先的资源
const NETWORK_FIRST = [
    '/api/',
    '/user_status.php',
    '/login.php',
    '/register.php'
];

// 需要缓存优先的资源
const CACHE_FIRST = [
    '/assets/css/',
    '/assets/js/',
    '/assets/images/',
    '/assets/fonts/'
];

// 安装事件 - 预缓存静态资源
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Service Worker: Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('Service Worker: Static assets cached');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Failed to cache static assets', error);
            })
    );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated');
                return self.clients.claim();
            })
    );
});

// 拦截请求
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // 跳过非GET请求
    if (request.method !== 'GET') {
        return;
    }
    
    // 跳过chrome-extension和其他协议
    if (!url.protocol.startsWith('http')) {
        return;
    }
    
    event.respondWith(handleRequest(request));
});

// 处理请求的主要逻辑
async function handleRequest(request) {
    const url = new URL(request.url);
    const pathname = url.pathname;
    
    try {
        // 网络优先策略 - API请求
        if (NETWORK_FIRST.some(pattern => pathname.includes(pattern))) {
            return await networkFirst(request);
        }
        
        // 缓存优先策略 - 静态资源
        if (CACHE_FIRST.some(pattern => pathname.includes(pattern))) {
            return await cacheFirst(request);
        }
        
        // HTML页面 - 网络优先，缓存降级
        if (pathname === '/' || pathname.endsWith('.html')) {
            return await networkFirst(request);
        }
        
        // 外部资源 - 缓存优先
        if (url.origin !== self.location.origin) {
            return await cacheFirst(request);
        }
        
        // 默认策略 - 网络优先
        return await networkFirst(request);
        
    } catch (error) {
        console.error('Service Worker: Request failed', error);
        
        // 如果是HTML请求失败，返回离线页面
        if (request.headers.get('accept').includes('text/html')) {
            const cache = await caches.open(STATIC_CACHE);
            return await cache.match('/index.html');
        }
        
        // 其他请求失败，返回网络错误
        return new Response('Network Error', {
            status: 408,
            headers: { 'Content-Type': 'text/plain' }
        });
    }
}

// 网络优先策略
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        // 如果网络请求成功，缓存响应
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        // 网络失败，尝试从缓存获取
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        throw error;
    }
}

// 缓存优先策略
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
        // 后台更新缓存
        updateCache(request);
        return cachedResponse;
    }
    
    // 缓存中没有，从网络获取
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        throw error;
    }
}

// 后台更新缓存
async function updateCache(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            await cache.put(request, networkResponse);
        }
    } catch (error) {
        // 静默失败，不影响用户体验
        console.log('Service Worker: Background cache update failed', error);
    }
}

// 消息处理
self.addEventListener('message', event => {
    const { type, payload } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'CACHE_URLS':
            cacheUrls(payload.urls);
            break;
            
        case 'CLEAR_CACHE':
            clearCache(payload.cacheName);
            break;
            
        default:
            console.log('Service Worker: Unknown message type', type);
    }
});

// 缓存指定URL
async function cacheUrls(urls) {
    try {
        const cache = await caches.open(DYNAMIC_CACHE);
        await cache.addAll(urls);
        console.log('Service Worker: URLs cached successfully');
    } catch (error) {
        console.error('Service Worker: Failed to cache URLs', error);
    }
}

// 清理指定缓存
async function clearCache(cacheName) {
    try {
        const deleted = await caches.delete(cacheName || DYNAMIC_CACHE);
        console.log('Service Worker: Cache cleared', deleted);
    } catch (error) {
        console.error('Service Worker: Failed to clear cache', error);
    }
}

// 定期清理过期缓存
self.addEventListener('periodicsync', event => {
    if (event.tag === 'cache-cleanup') {
        event.waitUntil(cleanupExpiredCache());
    }
});

// 清理过期缓存
async function cleanupExpiredCache() {
    const cache = await caches.open(DYNAMIC_CACHE);
    const requests = await cache.keys();
    const now = Date.now();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
    
    for (const request of requests) {
        const response = await cache.match(request);
        const dateHeader = response.headers.get('date');
        
        if (dateHeader) {
            const responseDate = new Date(dateHeader).getTime();
            if (now - responseDate > maxAge) {
                await cache.delete(request);
                console.log('Service Worker: Expired cache entry removed', request.url);
            }
        }
    }
}
