<?php
/**
 * 基础信息查询API代理
 * 支持民事诉讼信息查询和户籍信息查询
 * 直接转发请求到上游API，上游API负责token认证
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);

    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
        $name = $_GET['name'] ?? '';
        $idcard = $_GET['idcard'] ?? '';
        $lx = $_GET['lx'] ?? '1';
    } else {
        $token = $input['token'] ?? '';
        $name = $input['name'] ?? '';
        $idcard = $input['idcard'] ?? '';
        $lx = $input['lx'] ?? '1';
    }

    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    if (empty($name)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入姓名'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    if (empty($idcard)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入身份证号'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 验证身份证号格式
    if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $idcard)) {
        echo json_encode([
            'code' => 400,
            'message' => '身份证号格式不正确'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 验证查询类型
    if (!in_array($lx, ['1', '2'])) {
        echo json_encode([
            'code' => 400,
            'message' => '查询类型参数错误'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 构建上游API请求URL
    $upstreamUrl = 'http://154.12.95.17:4657/api/dujia/index.php';
    $params = [
        'token' => $token,
        'name' => $name,
        'idcard' => $idcard,
        'lx' => $lx
    ];

    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;

    // 发起HTTP请求到上游API
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 30,
            'header' => [
                'User-Agent: iDatas-Proxy/1.0',
                'Accept: application/json'
            ]
        ]
    ]);

    $response = file_get_contents($fullUrl, false, $context);

    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => '查询服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 解析响应
    $result = json_decode($response, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => '查询结果解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
