<?php
/**
 * 白底个户API
 * 生成白底样式的个人户籍信息图片
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
        $name = $_GET['name'] ?? '';
        $idcard = $_GET['idcard'] ?? '';
    } else {
        $token = $input['token'] ?? '';
        $name = $input['name'] ?? '';
        $idcard = $input['idcard'] ?? '';
    }
    
    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($name)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入姓名'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($idcard)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入身份证号'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证姓名格式（2-4个中文字符）
    if (!preg_match('/^[\x{4e00}-\x{9fa5}]{2,4}$/u', $name)) {
        echo json_encode([
            'code' => 400,
            'message' => '姓名格式不正确，请输入2-4个中文字符'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证身份证号格式
    if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $idcard)) {
        echo json_encode([
            'code' => 400,
            'message' => '身份证号格式不正确'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建上游API请求URL
    $upstreamUrl = 'http://154.12.95.17:4657/api/gh1/';
    $params = [
        'token' => $token,
        'xm' => $name,
        'hm' => $idcard
    ];
    
    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;
    
    // 使用cURL进行更可靠的HTTP请求
    $response = makeHttpRequest($fullUrl);
    
    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => '白底个户服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => '白底个户结果解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 使用cURL进行HTTP请求，支持重试机制
 */
function makeHttpRequest($url, $maxRetries = 3) {
    $retryCount = 0;
    
    while ($retryCount < $maxRetries) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 15,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_USERAGENT => 'iDatas-WhiteBG/1.0',
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
                'Cache-Control: no-cache'
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        // 请求成功
        if ($response !== false && $httpCode == 200) {
            return $response;
        }
        
        $retryCount++;
        
        // 如果不是最后一次重试，等待一段时间再重试
        if ($retryCount < $maxRetries) {
            usleep(1000000); // 等待1秒，因为图片生成可能需要更长时间
        }
    }
    
    return false;
}
?>
