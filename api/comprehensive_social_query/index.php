<?php
/**
 * 综合社工查询API
 * 支持多种信息的综合查询
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
        $msg = $_GET['msg'] ?? '';
    } else {
        $token = $input['token'] ?? '';
        $msg = $input['msg'] ?? '';
    }
    
    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($msg)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入查询信息'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证查询信息格式（支持手机号、身份证号、姓名等）
    $msg = trim($msg);
    if (strlen($msg) < 2) {
        echo json_encode([
            'code' => 400,
            'message' => '查询信息长度至少2个字符'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建上游API请求URL
    $upstreamUrl = 'http://154.12.95.17:4657/api/sgzh/';
    $params = [
        'token' => $token,
        'msg' => $msg
    ];
    
    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;
    
    // 使用简单的file_get_contents进行HTTP请求
    $response = @file_get_contents($fullUrl);
    
    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => '综合社工查询服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => '综合社工查询结果解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理响应数据，解析shuju字段中的结构化数据
    if (isset($result['shuju']) && !empty($result['shuju'])) {
        $parsedData = parseSocialQueryData($result['shuju']);
        $result['parsed_data'] = $parsedData;
    }
    
    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 解析综合社工查询数据
 */
function parseSocialQueryData($shuju) {
    $data = [
        'sections' => [],
        'summary' => []
    ];
    
    // 按双换行分割不同的数据块
    $sections = explode("\n\n", $shuju);
    
    foreach ($sections as $section) {
        $section = trim($section);
        if (empty($section)) continue;
        
        $sectionData = [];
        $lines = explode("\n", $section);
        
        // 获取标题
        $title = '';
        if (!empty($lines[0])) {
            $firstLine = $lines[0];
            if (strpos($firstLine, '🏷数据标签→') !== false) {
                $title = str_replace('🏷数据标签→', '', $firstLine);
                $sectionData['type'] = 'data_label';
            } elseif (strpos($firstLine, '🔍智能扩展→') !== false) {
                $title = str_replace('🔍智能扩展→', '', $firstLine);
                $sectionData['type'] = 'smart_analysis';
            } elseif (strpos($firstLine, '🔗关联扩展→') !== false) {
                $title = str_replace('🔗关联扩展→', '', $firstLine);
                $sectionData['type'] = 'related_info';
            } else {
                $title = $firstLine;
                $sectionData['type'] = 'other';
            }
        }
        
        $sectionData['title'] = $title;
        $sectionData['content'] = [];
        
        // 解析内容
        for ($i = 1; $i < count($lines); $i++) {
            $line = trim($lines[$i]);
            if (empty($line)) continue;
            
            if (strpos($line, ':') !== false) {
                $parts = explode(':', $line, 2);
                if (count($parts) == 2) {
                    $key = trim($parts[0]);
                    $value = trim($parts[1]);
                    $sectionData['content'][$key] = $value;
                }
            } else {
                $sectionData['content'][] = $line;
            }
        }
        
        if (!empty($sectionData['title']) || !empty($sectionData['content'])) {
            $data['sections'][] = $sectionData;
        }
    }
    
    // 生成摘要信息
    $data['summary'] = generateSummary($data['sections']);
    
    return $data;
}

/**
 * 生成摘要信息
 */
function generateSummary($sections) {
    $summary = [
        'total_sections' => count($sections),
        'data_types' => [],
        'key_info' => []
    ];
    
    foreach ($sections as $section) {
        if (!in_array($section['type'], $summary['data_types'])) {
            $summary['data_types'][] = $section['type'];
        }
        
        // 提取关键信息
        if (isset($section['content']['姓名'])) {
            $summary['key_info']['names'][] = $section['content']['姓名'];
        }
        if (isset($section['content']['身份证'])) {
            $summary['key_info']['idcards'][] = $section['content']['身份证'];
        }
        if (isset($section['content']['电话']) || isset($section['content']['联系电话'])) {
            $phone = $section['content']['电话'] ?? $section['content']['联系电话'];
            $summary['key_info']['phones'][] = $phone;
        }
    }
    
    // 去重
    if (isset($summary['key_info']['names'])) {
        $summary['key_info']['names'] = array_unique($summary['key_info']['names']);
    }
    if (isset($summary['key_info']['idcards'])) {
        $summary['key_info']['idcards'] = array_unique($summary['key_info']['idcards']);
    }
    if (isset($summary['key_info']['phones'])) {
        $summary['key_info']['phones'] = array_unique($summary['key_info']['phones']);
    }
    
    return $summary;
}
?>
