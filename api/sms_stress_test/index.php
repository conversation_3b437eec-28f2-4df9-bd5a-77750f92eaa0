<?php
/**
 * 短信在线测压API
 * VIP功能 - 短信在线测压服务
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
        $phone = $_GET['phone'] ?? '';
    } else {
        $token = $input['token'] ?? '';
        $phone = $input['phone'] ?? '';
    }
    
    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($phone)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入手机号'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证手机号格式
    if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        echo json_encode([
            'code' => 400,
            'message' => '手机号格式不正确，请输入11位有效手机号'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建上游API请求URL
    $upstreamUrl = 'http://154.12.95.17:4657/api/xxcy/';
    $params = [
        'token' => $token,
        'phone' => $phone
    ];
    
    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;
    
    // 使用cURL进行更可靠的HTTP请求
    $response = makeHttpRequest($fullUrl);
    
    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => '短信测压服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => '短信测压服务响应解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理响应数据，增强返回信息
    if (isset($result['code']) && $result['code'] == 200) {
        // 成功响应，增加一些友好的提示信息
        if (isset($result['data'])) {
            $result['data']['service_name'] = '短信在线测压';
            $result['data']['warning'] = '请合理使用此功能，遵守相关法律法规';
            
            // 格式化时间显示
            if (isset($result['data']['submit_time'])) {
                $result['data']['submit_time_formatted'] = date('Y-m-d H:i:s', strtotime($result['data']['submit_time']));
            }
        }
        
        // 添加使用说明
        $result['usage_info'] = [
            'description' => '短信测压任务已提交',
            'estimated_duration' => '默认5分钟',
            'note' => '请耐心等待测压完成，期间请勿重复提交'
        ];
    }
    
    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 使用cURL进行HTTP请求，支持重试机制
 */
function makeHttpRequest($url, $maxRetries = 3) {
    $retryCount = 0;
    
    while ($retryCount < $maxRetries) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 15,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_USERAGENT => 'iDatas-SMSStressTest/1.0',
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
                'Cache-Control: no-cache'
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        // 请求成功
        if ($response !== false && $httpCode == 200) {
            return $response;
        }
        
        $retryCount++;
        
        // 如果不是最后一次重试，等待一段时间再重试
        if ($retryCount < $maxRetries) {
            usleep(1000000); // 等待1秒
        }
    }
    
    return false;
}
?>
