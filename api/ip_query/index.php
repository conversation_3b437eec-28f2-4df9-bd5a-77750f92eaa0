<?php
/**
 * IP信息查询API
 * 免费功能，查询IP地址的地理位置和详细信息
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $ip = $_GET['ip'] ?? '';
    } else {
        $ip = $input['ip'] ?? '';
    }
    
    // 如果没有提供IP，使用客户端IP
    if (empty($ip)) {
        $ip = getClientIP();
    }
    
    // 验证IP地址格式
    if (!filter_var($ip, FILTER_VALIDATE_IP)) {
        echo json_encode([
            'code' => 400,
            'message' => 'IP地址格式不正确'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 查询IP信息
    $result = queryIPInfo($ip);
    
    if ($result['success']) {
        echo json_encode([
            'code' => 200,
            'message' => 'IP信息查询成功',
            'ip' => $ip,
            'data' => $result['data']
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    } else {
        echo json_encode([
            'code' => 500,
            'message' => 'IP信息查询失败：' . $result['error']
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取客户端真实IP
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

/**
 * 查询IP信息
 */
function queryIPInfo($ip) {
    try {
        // 使用多个API作为备用方案
        $apis = [
            "https://ipapi.co/{$ip}/json/",
            "http://ip-api.com/json/{$ip}?lang=zh-CN",
            "https://api.ipgeolocation.io/ipgeo?apiKey=free&ip={$ip}"
        ];
        
        foreach ($apis as $apiUrl) {
            $result = callAPI($apiUrl);
            if ($result['success']) {
                return [
                    'success' => true,
                    'data' => $result['data']
                ];
            }
        }
        
        return ['success' => false, 'error' => '所有API都无法访问'];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * 调用API
 */
function callAPI($url) {
    try {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'header' => [
                    'Accept: application/json',
                    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8'
                ]
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            return ['success' => false, 'error' => '网络请求失败'];
        }
        
        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['success' => false, 'error' => 'JSON解析失败'];
        }
        
        // 检查API特定的错误
        if (isset($data['error']) && $data['error']) {
            return ['success' => false, 'error' => $data['reason'] ?? '查询失败'];
        }
        
        if (isset($data['status']) && $data['status'] === 'fail') {
            return ['success' => false, 'error' => $data['message'] ?? '查询失败'];
        }
        
        // 标准化数据格式
        $standardData = standardizeData($data, $url);
        
        return [
            'success' => true,
            'data' => $standardData
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * 标准化不同API的数据格式
 */
function standardizeData($data, $apiUrl) {
    $standard = [
        'ip' => '',
        'country' => '',
        'country_code' => '',
        'region' => '',
        'city' => '',
        'postal' => '',
        'latitude' => '',
        'longitude' => '',
        'timezone' => '',
        'isp' => '',
        'org' => '',
        'as' => '',
        'api_source' => ''
    ];
    
    // 根据不同API源进行数据映射
    if (strpos($apiUrl, 'ipapi.co') !== false) {
        // ipapi.co格式
        $standard['ip'] = $data['ip'] ?? '';
        $standard['country'] = $data['country_name'] ?? '';
        $standard['country_code'] = $data['country_code'] ?? '';
        $standard['region'] = $data['region'] ?? '';
        $standard['city'] = $data['city'] ?? '';
        $standard['postal'] = $data['postal'] ?? '';
        $standard['latitude'] = $data['latitude'] ?? '';
        $standard['longitude'] = $data['longitude'] ?? '';
        $standard['timezone'] = $data['timezone'] ?? '';
        $standard['isp'] = $data['org'] ?? '';
        $standard['org'] = $data['org'] ?? '';
        $standard['as'] = $data['asn'] ?? '';
        $standard['api_source'] = 'ipapi.co';
    } elseif (strpos($apiUrl, 'ip-api.com') !== false) {
        // ip-api.com格式
        $standard['ip'] = $data['query'] ?? '';
        $standard['country'] = $data['country'] ?? '';
        $standard['country_code'] = $data['countryCode'] ?? '';
        $standard['region'] = $data['regionName'] ?? '';
        $standard['city'] = $data['city'] ?? '';
        $standard['postal'] = $data['zip'] ?? '';
        $standard['latitude'] = $data['lat'] ?? '';
        $standard['longitude'] = $data['lon'] ?? '';
        $standard['timezone'] = $data['timezone'] ?? '';
        $standard['isp'] = $data['isp'] ?? '';
        $standard['org'] = $data['org'] ?? '';
        $standard['as'] = $data['as'] ?? '';
        $standard['api_source'] = 'ip-api.com';
    } elseif (strpos($apiUrl, 'ipgeolocation.io') !== false) {
        // ipgeolocation.io格式
        $standard['ip'] = $data['ip'] ?? '';
        $standard['country'] = $data['country_name'] ?? '';
        $standard['country_code'] = $data['country_code2'] ?? '';
        $standard['region'] = $data['state_prov'] ?? '';
        $standard['city'] = $data['city'] ?? '';
        $standard['postal'] = $data['zipcode'] ?? '';
        $standard['latitude'] = $data['latitude'] ?? '';
        $standard['longitude'] = $data['longitude'] ?? '';
        $standard['timezone'] = $data['time_zone']['name'] ?? '';
        $standard['isp'] = $data['isp'] ?? '';
        $standard['org'] = $data['organization'] ?? '';
        $standard['as'] = $data['asn'] ?? '';
        $standard['api_source'] = 'ipgeolocation.io';
    }
    
    return $standard;
}
?>
