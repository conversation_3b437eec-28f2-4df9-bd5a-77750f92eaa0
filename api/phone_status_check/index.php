<?php
/**
 * 手机号状态检测API
 * 检测手机号的状态信息
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
        $phone = $_GET['phone'] ?? '';
    } else {
        $token = $input['token'] ?? '';
        $phone = $input['phone'] ?? '';
    }
    
    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($phone)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入手机号'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证手机号格式
    if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        echo json_encode([
            'code' => 400,
            'message' => '手机号格式不正确'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建上游API请求URL
    $upstreamUrl = 'http://154.12.95.17:4657/api/khjc/';
    $params = [
        'token' => $token,
        'phone' => $phone
    ];
    
    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;
    
    // 使用cURL进行更可靠的HTTP请求
    $response = makeHttpRequest($fullUrl);
    
    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => '手机号状态检测服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => '手机号状态检测结果解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理响应数据，解析message字段中的结构化数据
    if (isset($result['message']) && !empty($result['message'])) {
        $parsedData = parsePhoneStatusData($result['message']);
        $result['parsed_data'] = $parsedData;
    }
    
    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 解析手机号状态检测数据
 */
function parsePhoneStatusData($message) {
    $data = [];
    
    // 按换行分割
    $lines = explode("\n", $message);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // 解析各种字段
        if (strpos($line, '手机号:') !== false) {
            $data['phone'] = trim(str_replace('手机号:', '', $line));
        } elseif (strpos($line, '检测结果:') !== false) {
            $data['status'] = trim(str_replace('检测结果:', '', $line));
        } elseif (strpos($line, '开通时间:') !== false) {
            $data['open_time'] = trim(str_replace('开通时间:', '', $line));
        }
    }
    
    return $data;
}

/**
 * 使用cURL进行HTTP请求，支持重试机制
 */
function makeHttpRequest($url, $maxRetries = 3) {
    $retryCount = 0;
    
    while ($retryCount < $maxRetries) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 15,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_USERAGENT => 'iDatas-PhoneStatus/1.0',
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
                'Cache-Control: no-cache'
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        // 请求成功
        if ($response !== false && $httpCode == 200) {
            return $response;
        }
        
        $retryCount++;
        
        // 如果不是最后一次重试，等待一段时间再重试
        if ($retryCount < $maxRetries) {
            usleep(500000); // 等待0.5秒
        }
    }
    
    return false;
}
?>
