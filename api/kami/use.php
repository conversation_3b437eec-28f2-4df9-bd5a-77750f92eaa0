<?php
/**
 * 卡密使用API
 * 调用上游API进行卡密使用
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
        $kami = $_GET['kami'] ?? '';
    } else {
        $token = $input['token'] ?? '';
        $kami = $input['kami'] ?? '';
    }
    
    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($kami)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入卡密'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证卡密格式（可选，根据需要调整）
    if (strlen($kami) < 6) {
        echo json_encode([
            'code' => 400,
            'message' => '卡密格式不正确'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建上游API请求URL
    $upstreamUrl = 'http://154.12.95.17:4657/admapi/sykami.php';
    $params = [
        'token' => $token,
        'kami' => $kami
    ];
    
    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;
    
    // 发起HTTP请求到上游API
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 30,
            'header' => [
                'User-Agent: iDatas-Kami/1.0',
                'Accept: application/json'
            ]
        ]
    ]);
    
    $response = file_get_contents($fullUrl, false, $context);
    
    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => '卡密服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => '卡密服务响应解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
