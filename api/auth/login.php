<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 用户数据文件路径
$usersFile = __DIR__ . '/users.json';

// 检查用户数据文件是否存在
if (!file_exists($usersFile)) {
    echo json_encode([
        "code" => 404,
        "message" => "用户数据文件不存在",
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode([
        "code" => 400,
        "message" => "无效的请求数据",
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$username = trim($input['username'] ?? '');
$password = $input['password'] ?? '';

// 验证输入
if (empty($username) || empty($password)) {
    echo json_encode([
        "code" => 400,
        "message" => "用户名和密码不能为空",
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 读取用户数据
    $users = json_decode(file_get_contents($usersFile), true);
    if (!is_array($users)) {
        echo json_encode([
            "code" => 500,
            "message" => "用户数据格式错误",
            "user" => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 查找用户
    $foundUser = null;
    $userIndex = -1;
    
    foreach ($users as $index => $user) {
        if ($user['username'] === $username) {
            $foundUser = $user;
            $userIndex = $index;
            break;
        }
    }

    if (!$foundUser) {
        echo json_encode([
            "code" => 404,
            "message" => "用户不存在",
            "user" => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 验证密码
    if (!password_verify($password, $foundUser['password'])) {
        echo json_encode([
            "code" => 401,
            "message" => "密码错误",
            "user" => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 更新登录信息
    $users[$userIndex]['last_login'] = date('Y-m-d H:i:s');
    $users[$userIndex]['login_count'] = ($foundUser['login_count'] ?? 0) + 1;

    // 保存更新后的用户数据
    file_put_contents($usersFile, json_encode($users, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

    // 返回用户信息（不包含密码）
    $userResponse = [
        'username' => $foundUser['username'],
        'token' => $foundUser['token'],
        'created_at' => $foundUser['created_at'],
        'last_login' => $users[$userIndex]['last_login'],
        'login_count' => $users[$userIndex]['login_count'],
        'vipcode' => $foundUser['vipcode'] ?? 0,
        'viptime' => $foundUser['viptime'] ?? '',
        'tokencode' => $foundUser['tokencode'] ?? 200
    ];

    echo json_encode([
        "code" => 200,
        "message" => "登录成功",
        "user" => $userResponse
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        "code" => 500,
        "message" => "服务器内部错误：" . $e->getMessage(),
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
}
?>
